// import 'dart:io';

// class UserData {
//   final String? firstName;
//   final String? middleName;
//   final String? lastName;
//   final String? phonecode;
//   final String? phoneNumber;
//   final String? email;
//   final File? profilePhoto;
//   final String? drivingLicence;
//   final String? latraStricker;
//   final String? registrationCard;
//   final String? plateNumber;
//   final String? userType;
//   final int? regionId;
//   final String? referralCode;
//   final String? password;
//   final int? isVerified;
//   final String? uid;
//   final String? otp;
//   final String? otpExpiresAt;

//   UserData({
//     this.firstName,
//     this.middleName,
//     this.lastName,
//     this.phonecode,
//     this.phoneNumber,
//     this.email,
//     this.profilePhoto,
//     this.drivingLicence,
//     this.latraStricker,
//     this.registrationCard,
//     this.plateNumber,
//     this.userType,
//     this.isVerified,
//     this.uid,
//     this.otp,
//     this.otpExpiresAt,
//     this.regionId,
//     this.referralCode,
//     this.password,
//   });

//   factory UserData.fromJson(Map<String, dynamic> json) {
//     return UserData(
//       firstName: json['first_name'] as String?,
//       middleName: json['middle_name'] as String?,
//       lastName: json['last_name'] as String?,
//       phonecode: json['phonecode'] as String?,
//       phoneNumber: json['phone_number'] as String?,
//       email: json['email'] as String?,
//       profilePhoto: json['profile_photo'] as File?,
//       drivingLicence: json['driving_licence'] as String?,
//       latraStricker: json['latra_stricker'] as String?,
//       registrationCard: json['registration_card'] as String?,
//       plateNumber: json['plate_number'] as String?,
//       userType: json['user_type'] as String?,
//       isVerified: json['is_verified'] as int?,
//       uid: json['uid'] as String?,
//       otp: json['otp'] as String?,
//       otpExpiresAt: json['otp_expires_at'] as String?,
//       regionId: json['region_id'] as int?,
//       referralCode: json['referral_code'] as String?,
//       // password: json['password'] as String?,
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'first_name': firstName,
//       'middle_name': middleName,
//       'last_name': lastName,
//       'phonecode': phonecode,
//       'phone_number': phoneNumber,
//       'email': email,
//       'profile_photo': profilePhoto,
//       'driving_licence': drivingLicence,
//       'latra_stricker': latraStricker,
//       'registration_card': registrationCard,
//       'plate_number': plateNumber,
//       'user_type': userType,
//       'is_verified': isVerified,
//       'uid': uid,
//       'otp': otp,
//       'otp_expires_at': otpExpiresAt,
//     };
//   }
// }

import 'dart:convert';
import 'dart:io';

class UserData {
  final String firstName;
  final String lastName;
  final String phoneNumber;
  final String email;
  final String password;
  final String userType;
  final int regionId;
  final String? middleName;
  final String? referralCode;
  final File? profilePhoto;
  final String? businessDescription;
  final String? businessName;
  final String? businessType;
  final String? businessAddress;        
  final String? businessLicenseNumber;
  final String? profilePhotoUrl;
  final double? walletBalanceTzs;
  final double? walletBalanceUsd;
  final String? preferredPaymentMethod;
  final bool? isVerified;
  final int? totalRides;
  final double? averageRating;
  final String? entityType;
  final String? referenceNumber;
  final String? phonecode;
  final String? companyId;
  final int? loginAttempts;
  final String? driverLicenseNumber;
  final String? licenseExpiryDate;
  final String? rating; 
  final int? totalRatings;
  final int? totalDeliveries;
  final String? dob;
  final String? oauthProvider;
  final String? oauthId;
  final String? fcmToken;
  final int? countryId;
  final int? districtId;
  final String? createdBy;
  final String? updatedBy;
  final String? address;
  final String? ward;
  final String? street;
  final String? houseNumber;
  final String? postalCode;
  final double? latitude;
  final double? longitude;
  final String? locationUpdatedAt;
  final String? status;
  final String? attachment;
  final String? drivingLicenseFile;
  final String? vehiclePlateNumber;
  final String? approvedBy;
  final String? approvedDate;
  final int? wid;
  final String? stid;
  final String? wfstatus;
  final int? requserinput;
  final int? privacyChecked;
  final int? isOnline;
  final int? isAvailable;
  final String? deletedAt;
  final String? createdAt;
  final String? updatedAt;

  const UserData({
    required this.firstName,
    required this.lastName,
    required this.phoneNumber,
    required this.email,
    required this.password,
    required this.userType,
    required this.regionId,
    this.middleName,
    this.referralCode,
    this.profilePhoto,
    this.businessDescription,
    this.businessName,
    this.businessType,
    this.businessAddress,        
    this.businessLicenseNumber,
    this.profilePhotoUrl,
    this.walletBalanceTzs,
    this.walletBalanceUsd,
    this.preferredPaymentMethod,
    this.isVerified,
    this.totalRides,
    this.averageRating,
    this.entityType,
    this.referenceNumber,
    this.phonecode,
    this.companyId,
    this.loginAttempts,
    this.driverLicenseNumber,
    this.licenseExpiryDate,
    this.rating,
    this.totalRatings,
    this.totalDeliveries,
    this.dob,
    this.oauthProvider,
    this.oauthId,
    this.fcmToken,
    this.countryId,
    this.districtId,
    this.createdBy,
    this.updatedBy,
    this.address,
    this.ward,
    this.street,
    this.houseNumber,
    this.postalCode,
    this.latitude,
    this.longitude,
    this.locationUpdatedAt,
    this.status,
    this.attachment,
    this.drivingLicenseFile,
    this.vehiclePlateNumber,
    this.approvedBy,
    this.approvedDate,
    this.wid,
    this.stid,
    this.wfstatus,
    this.requserinput,
    this.privacyChecked,
    this.isOnline,
    this.isAvailable,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
  });

  // Convert to API-ready JSON
  Future<Map<String, dynamic>> toApiJson() async {
    final json = {
      'first_name': firstName,
      'last_name': lastName,
      'phonecode': '255', // Fixed for Tanzania
      'phone': phoneNumber,
      'email': email,
      'password': password,
      'password_confirmation': password,
      'user_type': userType,
      'region_id': regionId,
      'privacy_checked': '1',
      if (middleName != null) 'middle_name': middleName,
      if (referralCode != null) 'referal_code': referralCode,
      // if (drivingLicence != null) 'driving_licence': drivingLicence,
      // if (latraSticker != null) 'latra_sticker': latraSticker,
      // if (registrationCard != null) 'registration_card': registrationCard,
      // if (plateNumber != null) 'plate_number': plateNumber,
      // if (licenceExpiry != null) 'licence_expiry': licenceExpiry,
      if (businessDescription != null)
        'business_description': businessDescription,
    };

    // Handle file uploads if present
    if (profilePhoto != null) {
      final photoBytes = await profilePhoto!.readAsBytes();
      json['profile_photo'] = base64Encode(photoBytes);
    }

    return json;
  }

  // Factory constructor from JSON
  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      firstName: json['name'] ?? json['first_name'] ?? '',
      lastName: json['sname'] ?? json['last_name'] ?? '',
      phoneNumber: json['phone'] ?? json['phone_number'] ?? '',
      email: json['email'] ?? '',
      password: '', // Not provided in API response
      userType: json['role'] ?? json['user_type'] ?? 'customer',
      regionId: json['region_id'] ?? 1,
      middleName: json['mname'] ?? json['middle_name'],
      referralCode: json['referal_code'],
      businessDescription: json['business_description'],
      businessName: json['business_name'],
      businessType: json['business_type'],
      businessAddress: json['business_address'],        
      businessLicenseNumber: json['business_license_number'], 
      profilePhotoUrl: (json['profile_photo_url'] != null && !json['profile_photo_url'].contains('ui-avatars.com'))
          ? json['profile_photo_url']
          : null,
      walletBalanceTzs: double.tryParse(json['wallet_balance_tzs']?.toString() ?? '0'),
      walletBalanceUsd: double.tryParse(json['wallet_balance_usd']?.toString() ?? '0'),
      preferredPaymentMethod: json['preferred_payment_method'],
      isVerified: json['is_verified'] == 1 || json['is_verified'] == true,
      totalRides: json['total_rides'],
      averageRating: double.tryParse(json['rating']?.toString() ?? '0'),
      // New fields
      entityType: json['entity_type'],
      referenceNumber: json['reference_number'],
      phonecode: json['phonecode'],
      companyId: json['company_id'],
      loginAttempts: json['login_attempts'],
      driverLicenseNumber: json['driver_license_number'],
      licenseExpiryDate: json['license_expiry_date'],
      rating: json['rating']?.toString(),
      totalRatings: json['total_ratings'],
      totalDeliveries: json['total_deliveries'],
      dob: json['dob'],
      oauthProvider: json['oauth_provider'],
      oauthId: json['oauth_id'],
      fcmToken: json['fcm_token'],
      countryId: json['country_id'],
      districtId: json['district_id'],
      createdBy: json['created_by'],
      updatedBy: json['updated_by'],
      address: json['address'],
      ward: json['ward'],
      street: json['street'],
      houseNumber: json['house_number'],
      postalCode: json['postal_code'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      locationUpdatedAt: json['location_updated_at'],
      status: json['status'],
      attachment: json['attachment'],
      drivingLicenseFile: json['driving_license_file'],
      vehiclePlateNumber: json['vehicle_plate_number'],
      approvedBy: json['approved_by'],
      approvedDate: json['approved_date'],
      wid: json['wid'],
      stid: json['stid'],
      wfstatus: json['wfstatus'],
      requserinput: json['requserinput'],
      privacyChecked: json['privacy_checked'],
      isOnline: json['is_online'],
      isAvailable: json['is_available'],
      deletedAt: json['deleted_at'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'first_name': firstName,
      'last_name': lastName,
      'phone_number': phoneNumber,
      'email': email,
      'password': password,
      'user_type': userType,
      'region_id': regionId,
      'middle_name': middleName,
      'profile_photo_url': profilePhotoUrl,
      'wallet_balance_tzs': walletBalanceTzs,
      'wallet_balance_usd': walletBalanceUsd,
      'preferred_payment_method': preferredPaymentMethod,
      'is_verified': isVerified,
      'total_rides': totalRides,
      'average_rating': averageRating,
      'referral_code': referralCode,
      'business_name': businessName,
      'business_type': businessType,
      'business_description': businessDescription,
      'business_address': businessAddress,        
      'business_license_number': businessLicenseNumber,
      'entity_type': entityType,
      'reference_number': referenceNumber,
      'phonecode': phonecode,
      'company_id': companyId,
      'login_attempts': loginAttempts,
      'driver_license_number': driverLicenseNumber,
      'license_expiry_date': licenseExpiryDate,
      'rating': rating,
      'total_ratings': totalRatings,
      'total_deliveries': totalDeliveries,
      'dob': dob,
      'oauth_provider': oauthProvider,
      'oauth_id': oauthId,
      'fcm_token': fcmToken,
      'country_id': countryId,
      'district_id': districtId,
      'created_by': createdBy,
      'updated_by': updatedBy,
      'address': address,
      'ward': ward,
      'street': street,
      'house_number': houseNumber,
      'postal_code': postalCode,
      'latitude': latitude,
      'longitude': longitude,
      'location_updated_at': locationUpdatedAt,
      'status': status,
      'attachment': attachment,
      'driving_license_file': drivingLicenseFile,
      'vehicle_plate_number': vehiclePlateNumber,
      'approved_by': approvedBy,
      'approved_date': approvedDate,
      'wid': wid,
      'stid': stid,
      'wfstatus': wfstatus,
      'requserinput': requserinput,
      'privacy_checked': privacyChecked,
      'is_online': isOnline,
      'is_available': isAvailable,
      'deleted_at': deletedAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  // Copy with method for immutability
  UserData copyWith({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? email,
    String? password,
    String? userType,
    int? regionId,
    String? middleName,
    String? referralCode,
    File? profilePhoto,
    String? drivingLicence,
    String? latraSticker,
    String? registrationCard,
    String? plateNumber,
    String? licenceExpiry,
    String? businessDescription,
    String? profilePhotoUrl,
    double? walletBalanceTzs,
    double? walletBalanceUsd,
    String? preferredPaymentMethod,
    bool? isVerified,
    int? totalRides,
    double? averageRating,
    String? entityType,
    String? referenceNumber,
    String? phonecode,
    String? companyId,
    int? loginAttempts,
    String? driverLicenseNumber,
    String? licenseExpiryDate,
    String? rating,
    int? totalRatings,
    int? totalDeliveries,
    String? dob,
    String? oauthProvider,
    String? oauthId,
    String? fcmToken,
    int? countryId,
    int? districtId,
    String? createdBy,
    String? updatedBy,
    String? address,
    String? ward,
    String? street,
    String? houseNumber,
    String? postalCode,
    double? latitude,
    double? longitude,
    String? locationUpdatedAt,
    String? status,
    String? attachment,
    String? drivingLicenseFile,
    String? vehiclePlateNumber,
    String? approvedBy,
    String? approvedDate,
    int? wid,
    String? stid,
    String? wfstatus,
    int? requserinput,
    int? privacyChecked,
    int? isOnline,
    int? isAvailable,
    String? deletedAt,
    String? createdAt,
    String? updatedAt,
  }) {
    return UserData(
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      password: password ?? this.password,
      userType: userType ?? this.userType,
      regionId: regionId ?? this.regionId,
      middleName: middleName ?? this.middleName,
      referralCode: referralCode ?? this.referralCode,
      profilePhoto: profilePhoto ?? this.profilePhoto,
      // drivingLicence: drivingLicence ?? this.drivingLicence,
      // latraSticker: latraSticker ?? this.latraSticker,
      // registrationCard: registrationCard ?? this.registrationCard,
      // plateNumber: plateNumber ?? this.plateNumber,
      // licenceExpiry: licenceExpiry ?? this.licenceExpiry,
      businessDescription: businessDescription ?? this.businessDescription,
      profilePhotoUrl: profilePhotoUrl ?? this.profilePhotoUrl,
      walletBalanceTzs: walletBalanceTzs ?? this.walletBalanceTzs,
      walletBalanceUsd: walletBalanceUsd ?? this.walletBalanceUsd,
      preferredPaymentMethod:
          preferredPaymentMethod ?? this.preferredPaymentMethod,
      isVerified: isVerified ?? this.isVerified,
      totalRides: totalRides ?? this.totalRides,
      averageRating: averageRating ?? this.averageRating,
      entityType: entityType ?? this.entityType,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      phonecode: phonecode ?? this.phonecode,
      companyId: companyId ?? this.companyId,
      loginAttempts: loginAttempts ?? this.loginAttempts,
      driverLicenseNumber: driverLicenseNumber ?? this.driverLicenseNumber,
      licenseExpiryDate: licenseExpiryDate ?? this.licenseExpiryDate,
      rating: rating ?? this.rating,
      totalRatings: totalRatings ?? this.totalRatings,
      totalDeliveries: totalDeliveries ?? this.totalDeliveries,
      dob: dob ?? this.dob,
      oauthProvider: oauthProvider ?? this.oauthProvider,
      oauthId: oauthId ?? this.oauthId,
      fcmToken: fcmToken ?? this.fcmToken,
      countryId: countryId ?? this.countryId,
      districtId: districtId ?? this.districtId,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      address: address ?? this.address,
      ward: ward ?? this.ward,
      street: street ?? this.street,
      houseNumber: houseNumber ?? this.houseNumber,
      postalCode: postalCode ?? this.postalCode,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationUpdatedAt: locationUpdatedAt ?? this.locationUpdatedAt,
      status: status ?? this.status,
      attachment: attachment ?? this.attachment,
      drivingLicenseFile: drivingLicenseFile ?? this.drivingLicenseFile,
      vehiclePlateNumber: vehiclePlateNumber ?? this.vehiclePlateNumber,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedDate: approvedDate ?? this.approvedDate,
      wid: wid ?? this.wid,
      stid: stid ?? this.stid,
      wfstatus: wfstatus ?? this.wfstatus,
      requserinput: requserinput ?? this.requserinput,
      privacyChecked: privacyChecked ?? this.privacyChecked,
      isOnline: isOnline ?? this.isOnline,
      isAvailable: isAvailable ?? this.isAvailable,
      deletedAt: deletedAt ?? this.deletedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
