import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:sepesha_app/services/preferences.dart';

class BookingServices {
  BookingServices();
  final String baseUrl = dotenv.env['BASE_URL']!;

  static String _apiBaseUrl = dotenv.env['BASE_URL']!;


  static Future<List<Map<String, dynamic>>> sendUserLatLong(
      LatLng? pickUpLocation,
      LatLng? deriveryLocation,
      ) async {
    try {
      final token = await Preferences.instance.apiToken;
      final uri = Uri.parse(
        '$_apiBaseUrl/calculate-fare?pickup_latitude=${pickUpLocation?.latitude}&pickup_longitude=${pickUpLocation?.longitude}&delivery_latitude=${deriveryLocation?.latitude}&delivery_longitude=${deriveryLocation?.longitude}',
      );

      final response = await http.post(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      debugPrint('loaded data ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body); // <-- Decode here

        final vehicleTypes = responseData['data']['vehicle_types'];
        debugPrint('loaded data2 $vehicleTypes');
        return List<Map<String, dynamic>>.from(vehicleTypes);
      } else {
        throw Exception(
          'Failed to send locations. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Error sending user locations: $e');
      return [];
    }
  }
}
