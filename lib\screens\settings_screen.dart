import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sepesha_app/Utilities/app_color.dart';
import 'package:sepesha_app/Utilities/app_text_style.dart';
import 'package:sepesha_app/provider/localization_provider.dart';
import 'package:sepesha_app/l10n/app_localizations.dart';
import 'package:sepesha_app/provider/theme_provider.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      backgroundColor: AppColor.white2,
      appBar: AppBar(
        backgroundColor: AppColor.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColor.blackText),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          localizations.settings,
          style: AppTextStyle.heading3(AppColor.blackText),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Language Settings Section
                _buildLanguageSection(localizations),
                const SizedBox(height: 20),
                // Theme Settings Section
                _buildThemeSection(localizations),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageSection(AppLocalizations localizations) {
    return Container(
      decoration: BoxDecoration(
        color: AppColor.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColor.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.language_rounded,
                    color: AppColor.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        localizations.languageSettings,
                        style: AppTextStyle.paragraph2(AppColor.blackText).copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        localizations.selectLanguage,
                        style: AppTextStyle.subtext1(AppColor.grey),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Language Options
          Consumer<LocalizationProvider>(
            builder: (context, localizationProvider, child) {
              return Column(
                children: [
                  _buildLanguageOption(
                    title: localizations.swahili,
                    subtitle: 'Kiswahili',
                    isSelected: localizationProvider.isSwahili,
                    onTap: () => _changeLanguage(const Locale('sw'), localizations),
                  ),
                  const Divider(height: 1),
                  _buildLanguageOption(
                    title: localizations.english,
                    subtitle: 'English',
                    isSelected: localizationProvider.isEnglish,
                    onTap: () => _changeLanguage(const Locale('en'), localizations),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildThemeSection(AppLocalizations localizations) {
    return Container(
      decoration: BoxDecoration(
        color: AppColor.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColor.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColor.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.palette_rounded,
                    color: AppColor.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        localizations.themeSettings,
                        style: AppTextStyle.paragraph2(AppColor.blackText).copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        localizations.selectTheme,
                        style: AppTextStyle.subtext1(AppColor.grey),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Theme Options
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return Column(
                children: [
                  _buildThemeOption(
                    title: localizations.lightMode,
                    subtitle: 'Light theme',
                    icon: Icons.light_mode,
                    isSelected: themeProvider.isLight,
                    onTap: () => _changeTheme(ThemeMode.light, localizations),
                  ),
                  const Divider(height: 1),
                  _buildThemeOption(
                    title: localizations.darkMode,
                    subtitle: 'Dark theme',
                    icon: Icons.dark_mode,
                    isSelected: themeProvider.isDark,
                    onTap: () => _changeTheme(ThemeMode.dark, localizations),
                  ),
                  const Divider(height: 1),
                  _buildThemeOption(
                    title: localizations.systemMode,
                    subtitle: 'Follow system setting',
                    icon: Icons.settings_system_daydream,
                    isSelected: themeProvider.isSystem,
                    onTap: () => _changeTheme(ThemeMode.system, localizations),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageOption({
    required String title,
    required String subtitle,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              const SizedBox(width: 52), // Align with header content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTextStyle.paragraph2(AppColor.blackText).copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: AppTextStyle.subtext1(AppColor.grey),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: AppColor.primary,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check,
                    color: AppColor.white,
                    size: 16,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildThemeOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected 
                    ? AppColor.primary.withValues(alpha: 0.1)
                    : AppColor.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isSelected ? AppColor.primary : AppColor.grey,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyle.paragraph2(AppColor.blackText).copyWith(
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: AppTextStyle.subtext1(AppColor.grey),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppColor.primary,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _changeLanguage(Locale locale, AppLocalizations localizations) async {
    final localizationProvider = Provider.of<LocalizationProvider>(context, listen: false);
    
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
    
    await localizationProvider.setLocale(locale);
    
    // Close loading dialog
    if (mounted) {
      Navigator.of(context).pop();
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(localizations.languageChanged),
          backgroundColor: const Color(0xFF008000), // SUCCESS color from memory
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _changeTheme(
    ThemeMode newMode,
    AppLocalizations localizations,
  ) async {
    final provider = Provider.of<ThemeProvider>(context, listen: false);
    ThemeModeCustom custom = provider.themeModeCustom;

    switch (newMode) {
      case ThemeMode.dark:
        custom = ThemeModeCustom.dark;
        break;
      case ThemeMode.light:
        custom = ThemeModeCustom.light;
        break;
      case ThemeMode.system:
      default:
        custom = ThemeModeCustom.system;
        break;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );

    await provider.setThemeMode(custom);

    if (!mounted) return;
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(localizations.themeChanged),
        backgroundColor: AppColor.successColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
