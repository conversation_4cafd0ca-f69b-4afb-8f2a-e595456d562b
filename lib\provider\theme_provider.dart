import 'package:flutter/material.dart';
import 'package:sepesha_app/services/preferences.dart';

enum ThemeModeCustom { light, dark, system }

class ThemeProvider extends ChangeNotifier {
  ThemeModeCustom _themeMode = ThemeModeCustom.light;
  
  ThemeModeCustom get themeModeCustom => _themeMode;

  ThemeMode get themeMode {
    switch (_themeMode) {
      case ThemeModeCustom.light:
        return ThemeMode.light;
      case ThemeModeCustom.dark:
        return ThemeMode.dark;
      case ThemeModeCustom.system:
      default:
        return ThemeMode.system;
    }
  }

  
  ThemeProvider() {
    _loadSavedTheme();
  }
  
  Future<void> _loadSavedTheme() async {
    final savedTheme = await Preferences.instance.getString('app_theme');
    if (savedTheme != null) {
      switch (savedTheme) {
        case 'light':
          _themeMode = ThemeModeCustom.light;
          break;
        case 'dark':
          _themeMode = ThemeModeCustom.dark;
          break;
        case 'system':
          _themeMode = ThemeModeCustom.system;
          break;
      }
      notifyListeners();
    }
  }
  
  Future<void> setThemeMode(ThemeModeCustom mode) async {
    if (_themeMode == mode) return;
    
    _themeMode = mode;
    Preferences.instance.save('app_theme', mode.name);
    notifyListeners();
  }
  
  bool get isLight => _themeMode == ThemeModeCustom.light;
  bool get isDark => _themeMode == ThemeModeCustom.dark;
  bool get isSystem => _themeMode == ThemeModeCustom.system;
}