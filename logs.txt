SwapBuffersCompleted=706844497405955, DisplayPresentTime=0, 
[+1954619 ms] I/flutter (32148): Attempting to get current location...
[   +2 ms] I/flutter (32148): Permissions granted, getting position...
[+2285 ms] I/flutter (32148): Current location obtained: -6.8015488, 39.2123371
[+1893 ms] ══╡ EXCEPTION CAUGHT BY WIDGETS LIBRARY
╞═══════════════════════════════════════════════════════════
                    The following assertion was thrown building Consumer<dynamic>(dirty):
                    Tried to call Provider.of<dynamic>. This is likely a mistake and is therefore
                    unsupported.

                    If you want to expose a variable that can be anything, consider changing
                    `dynamic` to `Object` instead.
                    'package:provider/src/provider.dart':
                    Failed assertion: line 364 pos 7: 'T != dynamic'

                    The relevant error-causing widget was:
                      Consumer<dynamic>

Consumer:file:///C:/Users/<USER>/Desktop/Sepesha_app/lib/components/app_button.dart:148:16

                    When the exception was thrown, this was the stack:
                    #2      Provider._inheritedElementOf (package:provider/src/provider.dart:364:7)       
                    #3      Provider.of (package:provider/src/provider.dart:327:30)
                    #4      Consumer.buildWithChild (package:provider/src/consumer.dart:181:16)
                    #5      SingleChildStatelessWidget.build (package:nested/nested.dart:259:41)
                    #6      StatelessElement.build (package:flutter/src/widgets/framework.dart:5781:49)   
                    #7      SingleChildStatelessElement.build (package:nested/nested.dart:279:18)
                    #8      ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5715:15)
                    #9      Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #10     ComponentElement._firstBuild
(package:flutter/src/widgets/framework.dart:5697:5)
                    #11     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5691:5)    
                    #12     SingleChildWidgetElementMixin.mount (package:nested/nested.dart:222:11)       
                    ...     Normal element mounting (188 frames)
                    #200    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4539:16)    
                    #201    MultiChildRenderObjectElement.inflateWidget
(package:flutter/src/widgets/framework.dart:7159:36)
                    #202    MultiChildRenderObjectElement.mount
(package:flutter/src/widgets/framework.dart:7175:32)
                    #203    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4539:16)    
                    #204    MultiChildRenderObjectElement.inflateWidget
(package:flutter/src/widgets/framework.dart:7159:36)
                    #205    Element.updateChild (package:flutter/src/widgets/framework.dart:4004:18)      
                    #206    Element.updateChildren (package:flutter/src/widgets/framework.dart:4201:11)   
                    #207    MultiChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7192:17)
                    #208    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #209    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #210    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #211    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #212    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #213    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #214    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #215    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #216    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #217    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #218    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #219    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #220    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #221    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #222    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #223    SingleChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7015:14)
                    #224    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #225    SingleChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7015:14)
                    #226    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #227    SingleChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7015:14)
                    #228    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #229    SingleChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7015:14)
                    #230    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #231    SingleChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7015:14)
                    #232    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #233    SingleChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7015:14)
                    #234    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #235    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #236    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #237    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #238    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #239    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #240    SingleChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7015:14)
                    #241    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #242    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #243    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #244    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #245    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #246    SingleChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7015:14)
                    #247    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #248    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #249    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #250    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #251    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #252    SingleChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7015:14)
                    #253    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #254    SingleChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7015:14)
                    #255    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #256    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #257    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #258    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #259    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #260    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #261    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #262    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #263    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #264    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #265    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #266    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #267    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #268    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #269    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #270    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #271    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #272    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #273    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #274    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #275    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #276    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #277    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #278    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #279    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #280    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #281    StatelessElement.update (package:flutter/src/widgets/framework.dart:5787:5)   
                    #282    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #283    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #284    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #285    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #286    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #287    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #288    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #289    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #290    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #291    Element.updateChildren (package:flutter/src/widgets/framework.dart:4140:11)   
                    #292    MultiChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7192:17)
                    #293    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #294    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #295    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #296    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #297    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #298    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #299    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #300    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #301    StatelessElement.update (package:flutter/src/widgets/framework.dart:5787:5)   
                    #302    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #303    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #304    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #305    StatelessElement.update (package:flutter/src/widgets/framework.dart:5787:5)   
                    #306    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #307    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #308    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #309    StatelessElement.update (package:flutter/src/widgets/framework.dart:5787:5)   
                    #310    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #311    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #312    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #313    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #314    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #315    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #316    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #317    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #318    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #319    Element.updateChildren (package:flutter/src/widgets/framework.dart:4140:11)   
                    #320    MultiChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7192:17)
                    #321    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #322    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #323    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #324    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #325    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #326    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #327    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #328    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #329    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #330    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #331    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #332    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #333    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #334    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #335    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #336    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #337    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #338    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #339    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #340    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #341    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #342    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #343    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #344    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #345    SingleChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7015:14)
                    #346    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #347    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #348    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #349    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #350    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #351    SingleChildRenderObjectElement.update
(package:flutter/src/widgets/framework.dart:7015:14)
                    #352    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #353    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #354    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #355    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #356    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #357    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #358    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #359    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #360    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #361    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #362    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #363    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #364    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #365    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #366    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #367    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #368    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #369    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #370    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #371    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #372    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #373    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #374    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #375    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #376    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #377    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #378    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #379    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #380    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #381    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #382    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #383    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #384    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #385    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #386    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #387    StatefulElement.update (package:flutter/src/widgets/framework.dart:5899:5)    
                    #388    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #389    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #390    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #391    ProxyElement.update (package:flutter/src/widgets/framework.dart:6041:5)       
                    #392    _InheritedProviderScopeElement.update
(package:provider/src/inherited_provider.dart:536:11)
                    #393    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #394    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #395    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #396    StatelessElement.update (package:flutter/src/widgets/framework.dart:5787:5)   
                    #397    Element.updateChild (package:flutter/src/widgets/framework.dart:3982:15)      
                    #398    ComponentElement.performRebuild
(package:flutter/src/widgets/framework.dart:5738:16)
                    #399    StatefulElement.performRebuild
(package:flutter/src/widgets/framework.dart:5874:11)
                    #400    Element.rebuild (package:flutter/src/widgets/framework.dart:5427:7)
                    #401    BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)   
                    #402    BuildScope._flushDirtyElements
(package:flutter/src/widgets/framework.dart:2752:11)
                    #403    BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3056:18)    
                    #404    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1240:21)   
                    #405    RendererBinding._handlePersistentFrameCallback
(package:flutter/src/rendering/binding.dart:495:5)
                    #406    SchedulerBinding._invokeFrameCallback
(package:flutter/src/scheduler/binding.dart:1438:15)
                    #407    SchedulerBinding.handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1351:9)
                    #408    SchedulerBinding._handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1204:5)
                    #409    _invoke (dart:ui/hooks.dart:331:13)
                    #410    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)        
                    #411    _drawFrame (dart:ui/hooks.dart:303:31)
                    (elided 2 frames from class _AssertionError)


════════════════════════════════════════════════════════════════════════════════════════════════════      
[  +55 ms] Another exception was thrown: Tried to call Provider.of<dynamic>. This is likely a mistake and 
is
           therefore
[+11978 ms] E/BLASTBufferQueue(32148): [ViewRootImpl[MainActivity]#8](f:0,a:1) Applying pending
transactions on dtor 1
[+1055 ms] I/FA      (32148): Application backgrounded at: timestamp_millis: 1754077893195