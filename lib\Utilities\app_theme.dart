import 'package:flutter/material.dart';
import 'package:sepesha_app/Utilities/app_color.dart';

class AppTheme {
  static ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    primarySwatch: MaterialColor(
      AppColor.primary.value,
      <int, Color>{
        50: AppColor.primary.withValues(alpha: 0.1),
        100: AppColor.primary.withValues(alpha: 0.2),
        200: AppColor.primary.withValues(alpha: 0.3),
        300: AppColor.primary.withValues(alpha: 0.4),
        400: AppColor.primary.withValues(alpha: 0.5),
        500: AppColor.primary,
        600: AppColor.primary.withValues(alpha: 0.7),
        700: AppColor.primary.withValues(alpha: 0.8),
        800: AppColor.primary.withValues(alpha: 0.9),
        900: AppColor.primary,
      },
    ),
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColor.primary,
      brightness: Brightness.light,
    ),
    scaffoldBackgroundColor: AppColor.background,
    appBarTheme: AppBarTheme(
      backgroundColor: AppColor.white,
      foregroundColor: AppColor.blackText,
      elevation: 0,
    ),
  );

  static ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    primarySwatch: MaterialColor(
      AppColor.primary.value,
      <int, Color>{
        50: AppColor.primary.withValues(alpha: 0.1),
        100: AppColor.primary.withValues(alpha: 0.2),
        200: AppColor.primary.withValues(alpha: 0.3),
        300: AppColor.primary.withValues(alpha: 0.4),
        400: AppColor.primary.withValues(alpha: 0.5),
        500: AppColor.primary,
        600: AppColor.primary.withValues(alpha: 0.7),
        700: AppColor.primary.withValues(alpha: 0.8),
        800: AppColor.primary.withValues(alpha: 0.9),
        900: AppColor.primary,
      },
    ),
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColor.primary,
      brightness: Brightness.dark,
    ),
    scaffoldBackgroundColor: AppColor.greyColor,
    appBarTheme: AppBarTheme(
      backgroundColor: AppColor.greyColor,
      foregroundColor: AppColor.white,
      elevation: 0,
    ),
  );
}